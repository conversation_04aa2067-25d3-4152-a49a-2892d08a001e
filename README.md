# aws-account-configuration

This repository contains the configuration to set up the merchant AWS account for a specific environment. 
It does not contain any project related configs, they are in the projects itself located. 

## Steps to proceed with setup

Get credentials from AWS console by clicking on `Command line or programmatic access` next to `extended_developer` role in `limango Merchant SDLC` account:
![alt text](./assets/1.png)
![alt text](./assets/2.png)

Setup your AWS credentials for SDLC:
```shell
aws configure --profile merchant-sdlc
```

Paste the credentials acquired into the prompt. This is not it as you have to store aws_session_token as well:
```shell
vi ~/.aws/credentials
```

Add the session token to look like the image below:
![alt text](./assets/3.png)

Now you can generate k8s config file by running:
```shell
aws eks update-kubeconfig --name merchant-sdlc-eks --kubeconfig ~/.kube/kube-config-merchant-sdlc-eks --profile merchant-sdlc
```

Once the config is generated you can start working with `terraform` and `kubectl`. It is important to set the path to the config you are currently using by running:
```shell
export KUBECONFIG=~/.kube/kube-config-merchant-sdlc-eks
```

Now you are all set. 

**To do the same for production replace `sdlc` with `production` and follow the steps above.**
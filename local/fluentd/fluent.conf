<source>
  @type tail
  @id in_tail_container_logs
  @label @containers
  path /var/log/containers/*.log
  exclude_path ["/var/log/containers/cloudwatch-agent*", "/var/log/containers/fluentd*", "/var/log/containers/*_kube-system_*", "/var/log/containers/*_kube-monitoring_*"]
  pos_file /var/log/fluentd-opensearch-pdg-monitoring-service.log.pos
  tag *
  read_from_head true
  <parse>
    @type regexp
    expression /^(?<@timestamp>.+) (?<@stream>stdout|stderr) (.)? (?<message>.*)$/
  </parse>
</source>

<label @containers>
  # extract and parse gin logs
  <filter **>
    @type parser
    key_name message
    reserve_data true
    <parse>
      @type regexp
      expression /^\[GIN\]\s(?<time>.*)\s\|\s(?<code>\d+)\s\|\s+(?<elapsed>[\d\.]*)(?<elapsedUnit>.+)\s\|\s+(?<remote>.*)\s\|\s(?<method>\S*)\s+\"(?<path>.*)\"/
      time_format %Y/%m/%d - %H:%M:%S
    </parse>
  </filter>

  # extract and parse nginx logs
  <filter **>
    @type parser
    key_name message
    reserve_data true
    <parse>
      @type regexp
      expression /^(?<remote>[^ ]*) (?<host>[^ ]*) (?<user>[^ ]*) \[(?<time>[^\]]*)\] "(?<method>\S+)(?: +(?<path>[^\"]*?)(?: +\S*)?)?" (?<code>[^ ]*) (?<size>[^ ]*)(?: "(?<referer>[^\"]*)" "(?<agent>[^\"]*)"(?:\s+"(?<http_x_forwarded_for>[^\"]*)")?)?$/
      time_format %d/%b/%Y:%H:%M:%S %z
    </parse>
  </filter>

  # extract json string content and parse the json
  <filter **>
    @type parser
    key_name message
    reserve_data true
    <parse>
      @type json
    </parse>
  </filter>

    # take the "tag" field from event and store it in record for parsing
    <filter **>
      @type record_transformer
      <record>
        @source ${tag}
      </record>
    </filter>

  # parse "tag" field to extract the data
  <filter **>
    @type parser
    key_name @source
    reserve_data true
    <parse>
      @type regexp
      expression /^(.*\.)*(?<@serviceName>.*)-.*-.*_(?<@namespace>.*)_(?<@containerName>.*)-(?<@containerId>.*)\.log/
    </parse>
  </filter>

  # remove temp "@source" from record since we don't need anymore as we have already parsed all necessary info from it
  <filter **>
    @type record_transformer
    remove_keys @source
  </filter>

  <match **>
    @type stdout
  </match>

#  <match **>
#    @type opensearch
#    host opensearch
#    port 9201
#    scheme https
#    ssl_verify false
#
#    reload_connections false
#    reload_on_failure false
#    reconnect_on_error true
#    resurrect_after 5s
#    log_es_400_reason true
#    index_name ${@namespace}.${@serviceName}.${@containerName}.%Y%m%d
#    suppress_type_name on
#    suppress_type_name true
#    <buffer @namespace, @serviceName, @containerName, time>
#        timekey 10s
#        # see https://docs.aws.amazon.com/opensearch-service/latest/developerguide/limits.html#network-limits
#        chunk_limit_size 8MB
#        suppress_type_name true
#        queued_chunks_limit_size 8
#        overflow_action drop_oldest_chunk
#        flush_mode interval
#        flush_interval 10s
#        flush_thread_count 8
#        flush_at_shutdown true
#        retry_timeout 10m
#        retry_type periodic
#        retry_wait 1s
#    </buffer>
#  </match>
</label>
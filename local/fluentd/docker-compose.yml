version: "3"
services:
  fluentd:
    image: fluentd
    volumes:
      - ./pdg-monitoring-service.log:/var/log/containers/pdg-monitoring-service-57965b78f-85vd9_merchant-staging_productcountreader-c2153ec69650ed9fe3ab0e417b73ef7134854204d0e28cf231965a453e64b93f.log
      - ./merchant-fe.log:/var/log/containers/merchant-fe-68fcd9f9c4-pnrbm_merchant-production_nginx-20b9e6e3458e8aaa41c630a8f2d769b72f1b487a458cda10a0254ee750090a45.log
      - ./fluent.conf:/fluentd/etc/fluent.conf
      - ./fluentd-opensearch-pdg-monitoring-service.log.pos:/var/log/fluentd-opensearch-pdg-monitoring-service.log.pos

  opensearch:
    image: opensearchproject/opensearch:latest
    environment:
      - discovery.type=single-node
    volumes:
      - opensearch-data1:/usr/share/opensearch/data
    ports:
      - 9201:9200

volumes:
  opensearch-data1:
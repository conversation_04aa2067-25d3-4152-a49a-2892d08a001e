module "ecr" {
  source = "./ecr"
}

module "opensearch" {
  source = "../modules/opensearch"

  kms_key_descriptions = "Opensearch for logs"
  domain_name          = "opensearch"
  security_group_ids = ["sg-0b34fc581c46c7c79", "sg-08771545f8d28f0cf", "sg-030b22b89a1ce213e"]
  subnet_ids = ["subnet-0a003c7071d066c08"]
  node_count           = var.node_count
}

module "namespace-production" {
  source = "../modules/namespace"

  namespace = "merchant-production"
}

module "fluentd-production" {
  source = "../modules/fluentd"

  opensearch_host = "vpc-opensearch-ju6cckkd3wrs53r54nfnqvp7oi.eu-central-1.es.amazonaws.com"
}

module "ingress-merchant-fe-production" {
  source = "../modules/ingress/merchant-fe"

  merchant_fe_domain         = "partner.limango.de"
  merchant_cockpit_domain    = "cockpit.limango.de"
  merchant_center_api_domain = "merchant-center-api.limango.com"
  namespace                  = "merchant-production"
  certificate_arn            = "arn:aws:acm:eu-central-1:************:certificate/243b8a4b-6360-4fbd-bda3-6f8790c38fc5,arn:aws:acm:eu-central-1:************:certificate/1c819b19-a99a-4c62-afc2-24cac3d99b16"
}

module "ingress-internal-production" {
  source = "../modules/ingress/internal"

  iis_domain                      = "import-information-service-api.limango.com"
  master_data_domain              = "master-data-service-api.limango.com"
  price_comparison_service_domain = "price-comparison-service-api.limango.com"
  namespace                       = "merchant-production"
  certificate_arn                 = "arn:aws:acm:eu-central-1:************:certificate/1c819b19-a99a-4c62-afc2-24cac3d99b16"
  ingress_scheme                  = "internal"
  subnets                         = "subnet-0684b9afc09f0e4d5, subnet-04aabc327d9f3198a"
}

module "cognito" {
  source = "../modules/cognito"
  name   = "marketplace-production"
  domain = "limango-marketplace-auth-production"
  client_callback_urls = [
    "http://localhost:3000/auth/callback",
    "https://cockpit.limango.de/auth/callback"
  ]
  metadata_url = "https://login.microsoftonline.com/8794e153-c3bd-4479-8bea-61aeaf167d5a/federationmetadata/2007-06/federationmetadata.xml?appid=4549ca10-8022-4ea9-ae2b-ac51cfc13f72"
}

module "pimeventbridge-production" {
  source = "../modules/pimeventbridge"
  pim_bus_arn = "arn:aws:events:eu-central-1:************:event-bus/retail-pim-bridge-production"
  offer_bus_arn = "arn:aws:events:eu-central-1:************:event-bus/offers-production"
  account_id = "************"
  pim_account_id = "************"
}

module orderstateeventbridge-production {
    source = "../modules/orderstateeventbridge"
    account_id = "************"
    cross_account_id = "************"
}

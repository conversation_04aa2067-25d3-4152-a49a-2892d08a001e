module "ecr" {
  source = "./ecr"
}

module "opensearch" {
  source = "../modules/opensearch"

  kms_key_descriptions = "Opensearch for logs"
  domain_name          = "opensearch"
  security_group_ids   = ["sg-0c235c975c6bf366e", "sg-0f2265ff2b7ebc883", "sg-0e8e6bd430074d0b9"]
  subnet_ids           = ["subnet-04b3fb8dd4498d3e2"]
  node_count           = var.node_count
}

module "namespace-sandbox" {
  source = "../modules/namespace"

  namespace = "merchant-sandbox"
}

module "namespace-staging" {
  source = "../modules/namespace"

  namespace = "merchant-staging"
}

module "fluentd-sdlc" {
  source = "../modules/fluentd"

  opensearch_host = "vpc-opensearch-gpnmgdxy2o4f3iajbqgsrq3com.eu-central-1.es.amazonaws.com"
}

module "ingress-merchant-fe-sdlc" {
  source = "../modules/ingress/merchant-fe"

  merchant_fe_domain         = "staging-partner.limango.dev"
  merchant_cockpit_domain    = "staging-cockpit.limango.dev"
  merchant_center_api_domain = "staging-merchant-center-api.limango.dev"
  namespace                  = "merchant-staging"
  certificate_arn            = "arn:aws:acm:eu-central-1:************:certificate/7e41c142-9755-4452-a7fd-b585b6f525b7"
}

module "ingress-internal-sdlc" {
  source = "../modules/ingress/internal"

  iis_domain                      = "staging-import-information-service-api.limango.dev"
  master_data_domain              = "staging-master-data-service-api.limango.dev"
  price_comparison_service_domain = "staging-price-comparison-service-api.limango.dev"
  namespace                       = "merchant-staging"
  certificate_arn                 = "arn:aws:acm:eu-central-1:************:certificate/7e41c142-9755-4452-a7fd-b585b6f525b7"
  ingress_scheme                  = "internet-facing"
  subnets                         = "subnet-06cd33718fcaf7321, subnet-0ca3ed96a01c2ccb6"
}

module "cognito" {
  source = "../modules/cognito"

  name   = "marketplace-staging"
  domain = "limango-marketplace-auth-staging"
  client_callback_urls = [
    "http://localhost:3000/auth/callback",
    "https://staging-cockpit.limango.dev/auth/callback"
  ]
  metadata_url = "https://login.microsoftonline.com/8794e153-c3bd-4479-8bea-61aeaf167d5a/federationmetadata/2007-06/federationmetadata.xml?appid=d724238b-f3e0-47e9-9abb-79f207de0bcd"
}

module "pimeventbridge-sdlc" {
  source = "../modules/pimeventbridge"
  pim_bus_arn = "arn:aws:events:eu-central-1:************:event-bus/retail-pim-bridge-staging"
  offer_bus_arn = "arn:aws:events:eu-central-1:************:event-bus/offers-sandbox"
  account_id = "************"
  pim_account_id = "************"
}

module orderstateeventbridge-sdlc {
    source = "../modules/orderstateeventbridge"
    account_id = "************"
    cross_account_id = "************"
}


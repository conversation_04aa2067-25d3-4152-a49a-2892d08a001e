resource "aws_cognito_user_pool" "pool" {
  name = var.name

  deletion_protection = var.deletion_protection

  admin_create_user_config {
    allow_admin_create_user_only = true
  }

  schema {
    attribute_data_type = "String"
    name                = "ADGroups"
    string_attribute_constraints {
      min_length = "0"
      max_length = "2048"
    }
  }
}

resource "aws_cognito_user_pool_domain" "main" {
  domain       = var.domain
  user_pool_id = aws_cognito_user_pool.pool.id
}

resource "aws_cognito_user_pool_client" "marketplace" {
  name            = "marketplace"
  user_pool_id    = aws_cognito_user_pool.pool.id
  generate_secret = true

  callback_urls                        = var.client_callback_urls
  allowed_oauth_flows_user_pool_client = true
  access_token_validity                = 8
  id_token_validity                    = 8
  refresh_token_validity               = 7
  allowed_oauth_flows = ["code", "implicit"]
  allowed_oauth_scopes = ["email", "openid"]
  supported_identity_providers = ["ActiveDirectory"]
}

resource "aws_cognito_identity_provider" "active_directory" {
  count = 1

  user_pool_id  = aws_cognito_user_pool.pool.id
  provider_name = "ActiveDirectory"
  provider_type = "SAML"

  provider_details = {
    "IDPInit"            = "false"
    "IDPSignout"         = "false"
    "EncryptedResponses" = "false"
    "MetadataURL"        = var.metadata_url
  }

  attribute_mapping = {
    "email"           = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    "custom:ADGroups" = "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
  }
}

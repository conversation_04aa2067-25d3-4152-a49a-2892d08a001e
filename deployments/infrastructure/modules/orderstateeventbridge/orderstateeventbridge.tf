resource "aws_sqs_queue" "orderstatedlq" {
  name = "orderstatedlq"
}

resource "aws_sqs_queue" "orderstate" {
    name = "orderstatequeue.fifo"
    fifo_queue = "true"
    content_based_deduplication = "true"
}

resource "aws_sqs_queue_policy" "orderstate" {
    queue_url = aws_sqs_queue.orderstate.id
    policy    = jsonencode({
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "Owner",
                "Effect": "Allow",
                "Principal": {
                    "AWS": "arn:aws:iam::${var.account_id}:role/orderstateservice"
                },
                "Action": "SQS:*",
                "Resource": "arn:aws:sqs:eu-central-1:${var.account_id}:orderstatequeue.fifo"
            },
            {
                "Sid": "EventBridge",
                "Effect": "Allow",
                "Principal": {
                    "Service": "events.amazonaws.com"
                  },
                "Action": "sqs:SendMessage",
                "Resource": "arn:aws:sqs:eu-central-1:${var.account_id}:orderstatequeue.fifo",
                "Condition": {
                    "ArnEquals": {
                        "aws:SourceArn": "arn:aws:events:eu-central-1:${var.account_id}:rule/orderstate/orderstate"
                    }
                }
            }
        ]
    })
}

resource "aws_cloudwatch_log_group" "orderstate" {
  name = "/aws/events/orderstate"
  retention_in_days = 365
}

resource "aws_cloudwatch_event_bus" "orderstate" {
  name = "orderstate"
}

resource "aws_cloudwatch_event_bus_policy" "orderstate" {
  policy         = jsonencode({
    "Version": "2012-10-17",
    "Statement": [
      {
        "Sid": "AllowCrossAccountEventPosting",
        "Effect": "Allow",
        "Principal": {
          "AWS": "arn:aws:iam::${var.cross_account_id}:root"
        },
        "Action": "events:PutEvents",
        "Resource": "arn:aws:events:eu-central-1:${var.account_id}:event-bus/orderstate"
      }
    ]
  })
  event_bus_name = aws_cloudwatch_event_bus.orderstate.name
}

resource "aws_cloudwatch_event_rule" "orderstate" {
  name        = "orderstate"
  description = "Capture all orderstate events"

  event_pattern = jsonencode({ "source" : ["mc"] })
  event_bus_name = aws_cloudwatch_event_bus.orderstate.name
}

resource "aws_cloudwatch_event_target" "orderstate" {
  rule      = aws_cloudwatch_event_rule.orderstate.name
  target_id = "orderstate"
  arn       = aws_cloudwatch_log_group.orderstate.arn
  dead_letter_config {
    arn = aws_sqs_queue.orderstatedlq.arn
  }
  event_bus_name = aws_cloudwatch_event_bus.orderstate.name
}

resource "aws_cloudwatch_event_target" "orderstatequeuefifo" {
  rule      = aws_cloudwatch_event_rule.orderstate.name
  target_id = "orderstatequeuefifo"
  arn       = aws_sqs_queue.orderstate.arn
  dead_letter_config {
    arn = aws_sqs_queue.orderstatedlq.arn
  }
  event_bus_name = aws_cloudwatch_event_bus.orderstate.name
  sqs_target {
    message_group_id = "main"
  }
}



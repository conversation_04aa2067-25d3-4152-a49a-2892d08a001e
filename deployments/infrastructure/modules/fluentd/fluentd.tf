resource "kubernetes_manifest" "configmap_kube_monitoring_fluentd_opensearch_config" {
  manifest = {
    "apiVersion" = "v1"
    "data" = {
      "containers.conf" = templatefile("${path.module}/containers.conf.tmpl", {
        opensearch_host = var.opensearch_host
      })
      "fluent.conf" = file("${path.module}/fluent.conf")
    }
    "kind" = "ConfigMap"
    "metadata" = {
      "labels" = {
        "k8s-app" = "fluentd-opensearch"
      }
      "name" = "fluentd-opensearch-config"
      "namespace" = "kube-monitoring"
    }
  }
}

resource "kubernetes_manifest" "daemonset_kube_monitoring_fluentd_opensearch" {
  manifest = {
    "apiVersion" = "apps/v1"
    "kind" = "DaemonSet"
    "metadata" = {
      "name" = "fluentd-opensearch"
      "namespace" = "kube-monitoring"
    }
    "spec" = {
      "selector" = {
        "matchLabels" = {
          "k8s-app" = "fluentd-opensearch"
        }
      }
      "template" = {
        "metadata" = {
          "annotations" = {
            "configHash" = "c032cdfd2b2a5d01295fdcaeef8077ba065013af0c3b7c93ce8b3f262c8689df"
          }
          "labels" = {
            "k8s-app" = "fluentd-opensearch"
          }
        }
        "spec" = {
          "containers" = [
            {
              "env" = [
                {
                  "name" = "REGION"
                  "valueFrom" = {
                    "configMapKeyRef" = {
                      "key" = "logs.region"
                      "name" = "cluster-info"
                    }
                  }
                },
                {
                  "name" = "CLUSTER_NAME"
                  "valueFrom" = {
                    "configMapKeyRef" = {
                      "key" = "cluster.name"
                      "name" = "cluster-info"
                    }
                  }
                },
                {
                  "name" = "CI_VERSION"
                  "value" = "k8s/1.1.0"
                },
                {
                  "name" = "K8S_NODE_NAME"
                  "valueFrom" = {
                    "fieldRef" = {
                      "fieldPath" = "spec.nodeName"
                    }
                  }
                },
              ]
              "image" = "fluent/fluentd-kubernetes-daemonset:v1.16.2-debian-opensearch-1.0"
              "name" = "fluentd-opensearch"
              "resources" = {
                "limits" = {
                  "memory" = "3400Mi"
                }
                "requests" = {
                  "cpu" = "100m"
                  "memory" = "400Mi"
                }
              }
              "volumeMounts" = [
                {
                  "mountPath" = "/config-volume"
                  "name" = "config-volume"
                },
                {
                  "mountPath" = "/fluentd/etc"
                  "name" = "fluentdconf"
                },
                {
                  "mountPath" = "/var/log"
                  "name" = "varlog"
                },
                {
                  "mountPath" = "/var/lib/docker/containers"
                  "name" = "varlibdockercontainers"
                  "readOnly" = true
                },
                {
                  "mountPath" = "/run/log/journal"
                  "name" = "runlogjournal"
                  "readOnly" = true
                },
                {
                  "mountPath" = "/var/log/dmesg"
                  "name" = "dmesg"
                  "readOnly" = true
                },
              ]
            },
          ]
          "initContainers" = [
            {
              "command" = [
                "sh",
                "-c",
                "cp /config-volume/..data/* /fluentd/etc",
              ]
              "image" = "busybox"
              "name" = "copy-fluentd-opensearch-config"
              "volumeMounts" = [
                {
                  "mountPath" = "/config-volume"
                  "name" = "config-volume"
                },
                {
                  "mountPath" = "/fluentd/etc"
                  "name" = "fluentdconf"
                },
              ]
            },
            {
              "command" = [
                "sh",
                "-c",
                "",
              ]
              "image" = "busybox"
              "name" = "update-log-driver"
            },
          ]
          "serviceAccountName" = "fluentd"
          "terminationGracePeriodSeconds" = 30
          "volumes" = [
            {
              "configMap" = {
                "name" = "fluentd-opensearch-config"
              }
              "name" = "config-volume"
            },
            {
              "emptyDir" = {}
              "name" = "fluentdconf"
            },
            {
              "hostPath" = {
                "path" = "/var/log"
              }
              "name" = "varlog"
            },
            {
              "hostPath" = {
                "path" = "/var/lib/docker/containers"
              }
              "name" = "varlibdockercontainers"
            },
            {
              "hostPath" = {
                "path" = "/run/log/journal"
              }
              "name" = "runlogjournal"
            },
            {
              "hostPath" = {
                "path" = "/var/log/dmesg"
              }
              "name" = "dmesg"
            },
          ]
        }
      }
    }
  }
}

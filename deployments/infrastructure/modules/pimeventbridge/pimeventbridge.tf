resource "aws_iam_user" "mc" {
  name = "mc"
}

resource "aws_iam_user_policy" "mcpolicy" {
  name = "mcpolicy"
  user = aws_iam_user.mc.name
  policy = jsonencode({
    "Version": "2012-10-17",
    "Statement": [
      {
        "Sid": "EventBridgeActions",
        "Effect": "Allow",
        "Action": "events:PutEvents",
        "Resource": "*"
      }
    ]
  })
}

resource "aws_iam_role" "pimforwarding" {
  name = "pimforwarding"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        "Effect": "Allow",
        "Principal": {
          "AWS": "arn:aws:iam::${var.account_id}:root"
        },
        "Action": "sts:AssumeRole",
        "Condition": {}
      },
      {
        "Effect": "Allow",
        "Principal": {
          "Service": "events.amazonaws.com"
        },
        "Action": "sts:AssumeRole"
      }
    ]
  })

  inline_policy {
    name = "putpimevents"
    policy = jsonencode({
      "Version": "2012-10-17",
      "Statement": [
        {
          "Sid": "VisualEditor0",
          "Effect": "Allow",
          "Action": "events:PutEvents",
          "Resource": "arn:aws:events:eu-central-1:${var.pim_account_id}:event-bus/*"
        }
      ]
    })
  }
}

resource "aws_sqs_queue" "pimdlq" {
  name = "pimdlq"
}

resource "aws_cloudwatch_log_group" "pim" {
  name = "/aws/events/pim"
  retention_in_days = 365
}

resource "aws_cloudwatch_event_bus" "pim" {
  name = "pim"
}

resource "aws_cloudwatch_event_rule" "pim" {
  name        = "pim"
  description = "Capture all pim events"

  event_pattern = jsonencode({ "source" : ["mc"] })
  event_bus_name = aws_cloudwatch_event_bus.pim.name
}

resource "aws_cloudwatch_event_target" "cloudwatch" {
  rule      = aws_cloudwatch_event_rule.pim.name
  target_id = "Cloudwatch"
  arn       = aws_cloudwatch_log_group.pim.arn
  dead_letter_config {
    arn = aws_sqs_queue.pimdlq.arn
  }
  event_bus_name = aws_cloudwatch_event_bus.pim.name
}

resource "aws_cloudwatch_event_target" "pimforwarding" {
  rule      = aws_cloudwatch_event_rule.pim.name
  target_id = "PIM"
  arn       = var.pim_bus_arn
  role_arn = aws_iam_role.pimforwarding.arn
  dead_letter_config {
    arn = aws_sqs_queue.pimdlq.arn
  }
  event_bus_name = aws_cloudwatch_event_bus.pim.name
}

resource "aws_sqs_queue" "offerdlq" {
  name = "offerdlq"
}

resource "aws_cloudwatch_log_group" "offer" {
  name = "/aws/events/offer"
  retention_in_days = 365
}

resource "aws_cloudwatch_event_bus" "offer" {
  name = "offer"
}

resource "aws_cloudwatch_event_rule" "offer" {
  name        = "offer"
  description = "Capture all offer events"

  event_pattern = jsonencode({ "source" : ["mc"] })
  event_bus_name = aws_cloudwatch_event_bus.offer.name
}

resource "aws_cloudwatch_event_target" "offer" {
  rule      = aws_cloudwatch_event_rule.offer.name
  target_id = "Cloudwatch"
  arn       = aws_cloudwatch_log_group.offer.arn
  dead_letter_config {
    arn = aws_sqs_queue.offerdlq.arn
  }
  event_bus_name = aws_cloudwatch_event_bus.offer.name
}

resource "aws_cloudwatch_event_target" "offerforwarding" {
  rule      = aws_cloudwatch_event_rule.offer.name
  target_id = "OFFER"
  arn       = var.offer_bus_arn
  role_arn = aws_iam_role.pimforwarding.arn
  dead_letter_config {
    arn = aws_sqs_queue.offerdlq.arn
  }
  event_bus_name = aws_cloudwatch_event_bus.offer.name
}

resource "aws_kms_key" "opensearch" {
  description = var.kms_key_descriptions
}

resource "aws_opensearch_domain" "main" {
  domain_name    = var.domain_name
  engine_version = "OpenSearch_2.11"

  cluster_config {
    dedicated_master_count   = 0
    instance_count           = var.node_count
    instance_type            = "m5.large.search"
    dedicated_master_enabled = false
    zone_awareness_enabled   = false
  }

  ebs_options {
    ebs_enabled = true
    volume_size = "100"
    volume_type = "gp3"
  }

  vpc_options {
    security_group_ids = var.security_group_ids
    subnet_ids         = var.subnet_ids
  }

  encrypt_at_rest {
    enabled    = true
    kms_key_id = aws_kms_key.opensearch.id
  }

  node_to_node_encryption {
    enabled = true
  }

  tags = {
    Availability    = "A2"
    Confidentiality = "C2"
    Integrity       = "I1"
  }
}

resource "aws_elasticsearch_domain_policy" "main" {
  domain_name = aws_opensearch_domain.main.domain_name

  access_policies = jsonencode({
    Version   = "2012-10-17"
    Statement = [
      {
        Sid       = "ES"
        Effect    = "Allow"
        Principal = {
          AWS = "*"
        }
        Action = [
          "es:*"
        ]
        Resource = "${aws_opensearch_domain.main.arn}/*"
      }
    ]
  })
}

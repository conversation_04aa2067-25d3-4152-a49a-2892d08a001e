resource "kubernetes_ingress_v1" "merchant-fe-ingress" {
  metadata {
    name        = "merchant-fe-ingress"
    namespace   = var.namespace
    annotations = {
      "kubernetes.io/ingress.class"               = "alb"
      "alb.ingress.kubernetes.io/scheme"          = "internet-facing"
      "alb.ingress.kubernetes.io/target-type"     = "ip"
      "alb.ingress.kubernetes.io/certificate-arn" = var.certificate_arn
      "alb.ingress.kubernetes.io/ssl-redirect"    = 443
      "alb.ingress.kubernetes.io/listen-ports"    = jsonencode([{ HTTP = 80 }, { HTTPS = 443 }])
      "alb.ingress.kubernetes.io/group.name"      = "merchant-fe-ingress"
    }
    labels = {
      ingress = "alb"
    }
  }
  spec {
    rule {
      host = var.merchant_fe_domain
      http {
        path {
          path = "/v2*"
          backend {
            service {
              name = "merchant-fe-v2-service"
              port {
                number = 80
              }
            }
          }
        }
        path {
          path = "/*"
          backend {
            service {
              name = "merchant-fe-service"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    rule {
      host = var.merchant_cockpit_domain
      http {
        path {
          path = "/*"
          backend {
            service {
              name = "merchant-fe-v3-service"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    rule {
      host = var.merchant_center_api_domain
      http {
        path {
          path = "/*"
          backend {
            service {
              name = "merchant-be-service"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_ingress_v1" "internal-ingress" {
  metadata {
    name        = "internal-ingress"
    namespace   = var.namespace
    annotations = {
      "kubernetes.io/ingress.class"                = "alb"
      "alb.ingress.kubernetes.io/scheme"           = var.ingress_scheme
      "alb.ingress.kubernetes.io/target-type"      = "ip"
      "alb.ingress.kubernetes.io/certificate-arn"  = var.certificate_arn
      "alb.ingress.kubernetes.io/ssl-redirect"     = 443
      "alb.ingress.kubernetes.io/listen-ports"     = jsonencode([{ HTTP = 80 }, { HTTPS = 443 }])
      "alb.ingress.kubernetes.io/healthcheck-path" = "/v1/health"
      "alb.ingress.kubernetes.io/success-codes"    = "200-399"
      "alb.ingress.kubernetes.io/subnets"          = var.subnets
      "alb.ingress.kubernetes.io/group.name"      = "internal-ingress"
    }
    labels = {
      ingress = "alb"
    }
  }
  spec {
    rule {
      host = var.iis_domain
      http {
        path {
          path = "/*"
          backend {
            service {
              name = "import-information-service"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    rule {
      host = var.master_data_domain
      http {
        path {
          path = "/*"
          backend {
            service {
              name = "master-data-service"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
    rule {
      host = var.price_comparison_service_domain
      http {
        path {
          path = "/*"
          backend {
            service {
              name = "price-comparison-service"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
  }
}
